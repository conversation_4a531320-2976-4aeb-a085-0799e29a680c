# Codebase Summary

This is an ERP Project that uses supabase locally hosted for the backend, and react vite for the frontend with typescript , in react we are just using normal react with refine framework and for the styles we use normal div styles, and for tests we just do unit tests on functions using vitest.
Below is the folder structure of the codebase and the rules to follow when writing code in this project.

# Codebase Folder Structure

<!-- prettier-ignore-start -->
src/
├─ components/
│
├─ pages/
│  └─ {module}/
│     ├─ {Page}.page.tsx
│     ├─ functions.ts
│     └─ tests.ts
│
├─ utility/
│  ├─ supabaseClient.ts
│  ├─ auth.ts
│  ├─ utils.ts
│  ├─ constants.ts
│  ├─ auth.ts
│  ├─ colors.ts
│  ├─ i18n.ts
│  └─ translations/
│     ├─ ar.json
│     └─ en.json
│
├─ layouts/
│  └─ MainLayout.tsx
│
├─ app.tsx
└─ main.tsx
<!-- prettier-ignore-end -->

# Rules

- Make sure to use ant design with refine for this project using both packages @refinedev/antd and antd for all UI related components, the @refinedev/antd has all these components which are our priority to use : (ThemedLayoutV2, ThemedLayout, ThemedHeaderV2, ThemedSiderV2, ThemedTitleV2, AuthPage, Breadcrumb, FilterDropdown, AutoSaveIndicator, ErrorComponent, Inferencer, List, Create, Edit, Show, CloneButton, CreateButton, DeleteButton, EditButton, ExportButton, ImportButton, ListButton, RefreshButton, SaveButton, ShowButton, BooleanField, DateField, EmailField, FileField, ImageField, MarkdownField, NumberField, TagField, TextField, UrlField).
  Other components can use from the main antd library that the @refinedev/antd dont have.
- Use the refine framework for all pages and components, and use the refine hooks for data fetching and mutations.
- Use the supabaseClient from the src/utility/supabaseClient.ts file for all supabase related operations.
- Use the src/utility/constants.ts file for all constants used in the project.
- Use the src/utility/colors.ts file for all colors used in the project.
- Use the src/utility/i18n.ts file for all internationalization related operations.
- Use the translations files in src/utility/translations/ for all translations, and use the i18n.ts file to load the translations.
- Use the components folder for all reusable components, and make sure to follow the naming conventions for the components and do not duplicate components.
- Use the functions.ts file in each page folder for all functions related to that page, and make sure to follow the naming conventions for the functions (e.g. fetchData, handleSubmit, etc.).
- Use the tests.ts file in each page folder for all tests related to that page, and just do unit tests on functions.
- Use TypeScript for all files in the project, and make sure to follow the TypeScript best practices.
- Make sure to write clean, readable, and maintainable code, and make sure to follow the SOLID principles.
- Make sure to use the latest version of  all dependencies, and make sure to follow the dependency best practices.
- Use the .clinerules file for all rules related to the codebase, and make sure to follow the rules defined in this file.

