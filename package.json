{"name": "refine-app", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@refinedev/cli": "^2.16.21", "@refinedev/core": "^4.47.1", "@refinedev/devtools": "^1.1.32", "@refinedev/inferencer": "^5.1.1", "@refinedev/kbar": "^1.3.6", "@refinedev/react-hook-form": "^4.10.2", "@refinedev/react-router": "^1.0.0", "@refinedev/react-table": "^5.6.17", "@refinedev/supabase": "^5.7.4", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router": "^7.0.2"}, "devDependencies": {"@types/node": "^18.16.2", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "typescript": "^5.4.2", "vite": "^4.3.1"}, "scripts": {"dev": "refine dev", "build": "tsc && refine build", "start": "refine start", "refine": "refine"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "refine": {"projectId": "tWK0fd-Mv5s4N-fBgZsm"}}