                                            ?column?                                            
------------------------------------------------------------------------------------------------
 CREATE TABLE account_payable_open_items (                                                     +
     id bigint NOT NULL,                                                                       +
     supplier_id bigint NOT NULL,                                                              +
     bill_id bigint,                                                                           +
     debit_note_id bigint,                                                                     +
     payment_id bigint,                                                                        +
     open_amount numeric(18,2) NOT NULL,                                                       +
     paid_amount numeric(18,2) NOT NULL DEFAULT 0,                                             +
     due_date date,                                                                            +
     status varchar(20) NOT NULL DEFAULT 'open'::character varying,                            +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE account_receivable_open_items (                                                  +
     id bigint NOT NULL,                                                                       +
     customer_id bigint NOT NULL,                                                              +
     invoice_id bigint,                                                                        +
     credit_note_id bigint,                                                                    +
     receipt_id bigint,                                                                        +
     open_amount numeric(18,2) NOT NULL,                                                       +
     paid_amount numeric(18,2) NOT NULL DEFAULT 0,                                             +
     due_date date,                                                                            +
     status varchar(20) NOT NULL DEFAULT 'open'::character varying,                            +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE account_types (                                                                  +
     id bigint NOT NULL DEFAULT nextval('account_types_id_seq'::regclass),                     +
     name varchar(40) NOT NULL,                                                                +
     normal_balance varchar(6) NOT NULL,                                                       +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE accounts (                                                                       +
     id bigint NOT NULL DEFAULT nextval('accounts_id_seq'::regclass),                          +
     company_id bigint NOT NULL,                                                               +
     account_type_id bigint NOT NULL,                                                          +
     code varchar(20) NOT NULL,                                                                +
     cash_flow_category_id smallint,                                                           +
     name varchar(120) NOT NULL,                                                               +
     parent_account_id bigint,                                                                 +
     level_no smallint NOT NULL DEFAULT 1,                                                     +
     currency_id bigint,                                                                       +
     is_leaf boolean NOT NULL DEFAULT true,                                                    +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE allowance_types (                                                                +
     id bigint NOT NULL DEFAULT nextval('allowance_types_id_seq'::regclass),                   +
     code varchar(10) NOT NULL,                                                                +
     name varchar(120) NOT NULL,                                                               +
     taxable boolean NOT NULL DEFAULT true,                                                    +
     is_recurring boolean NOT NULL DEFAULT true,                                               +
     deleted_at timestamptz,                                                                   +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now()                                             +
 );                                                                                            +
 
 CREATE TABLE asset_assignments (                                                              +
     id bigint NOT NULL DEFAULT nextval('asset_assignments_id_seq'::regclass),                 +
     asset_id bigint NOT NULL,                                                                 +
     employee_id bigint,                                                                       +
     assigned_date date NOT NULL,                                                              +
     returned_date date,                                                                       +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE asset_attachments (                                                              +
     id bigint NOT NULL DEFAULT nextval('asset_attachments_id_seq'::regclass),                 +
     asset_id bigint NOT NULL,                                                                 +
     file_path text NOT NULL,                                                                  +
     file_type varchar(100),                                                                   +
     description text,                                                                         +
     uploaded_by uuid,                                                                         +
     uploaded_at timestamptz NOT NULL DEFAULT now(),                                           +
     deleted_at timestamptz,                                                                   +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now()                                             +
 );                                                                                            +
 
 CREATE TABLE asset_cost_histories (                                                           +
     id bigint NOT NULL DEFAULT nextval('asset_cost_histories_id_seq'::regclass),              +
     asset_id bigint NOT NULL,                                                                 +
     snapshot_date date NOT NULL,                                                              +
     cost numeric(18,2) NOT NULL,                                                              +
     accumulated_depreciation numeric(18,2) NOT NULL,                                          +
     notes text                                                                                +
 );                                                                                            +
 
 CREATE TABLE asset_depreciation_details (                                                     +
     id bigint NOT NULL DEFAULT nextval('asset_depreciation_details_id_seq'::regclass),        +
     run_id bigint NOT NULL,                                                                   +
     asset_id bigint NOT NULL,                                                                 +
     depreciation_amount numeric(18,2) NOT NULL,                                               +
     gl_journal_id bigint,                                                                     +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE asset_depreciation_header (                                                      +
     id bigint NOT NULL DEFAULT nextval('asset_depreciation_header_id_seq'::regclass),         +
     period_start date NOT NULL,                                                               +
     period_end date NOT NULL,                                                                 +
     posted_by uuid,                                                                           +
     posted_at timestamptz,                                                                    +
     is_posted boolean NOT NULL DEFAULT false,                                                 +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE asset_events (                                                                   +
     id bigint NOT NULL DEFAULT nextval('asset_events_id_seq'::regclass),                      +
     asset_id bigint NOT NULL,                                                                 +
     event_type varchar(20) NOT NULL,                                                          +
     event_subtype varchar(50),                                                                +
     event_date date NOT NULL,                                                                 +
     cost_change numeric(18,2),                                                                +
     proceeds_amount numeric(18,2),                                                            +
     gain_loss_amount numeric(18,2),                                                           +
     employee_from_id bigint,                                                                  +
     employee_to_id bigint,                                                                    +
     notes text                                                                                +
 );                                                                                            +
 
 CREATE TABLE asset_main_groups (                                                              +
     id bigint NOT NULL DEFAULT nextval('asset_main_groups_id_seq'::regclass),                 +
     code varchar(20) NOT NULL,                                                                +
     name varchar(100) NOT NULL,                                                               +
     gl_account_id bigint,                                                                     +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE asset_sub_groups (                                                               +
     id bigint NOT NULL DEFAULT nextval('asset_sub_groups_id_seq'::regclass),                  +
     main_group_id bigint NOT NULL,                                                            +
     code varchar(20) NOT NULL,                                                                +
     name varchar(100) NOT NULL,                                                               +
     depreciation_method varchar(30) NOT NULL DEFAULT 'SL'::character varying,                 +
     depreciation_rate numeric(5,2),                                                           +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE assets (                                                                         +
     id bigint NOT NULL DEFAULT nextval('assets_id_seq'::regclass),                            +
     asset_code varchar(30) NOT NULL,                                                          +
     description text NOT NULL,                                                                +
     main_group_id bigint,                                                                     +
     sub_group_id bigint,                                                                      +
     purchase_date date,                                                                       +
     purchase_cost numeric(18,2),                                                              +
     residual_value numeric(18,2),                                                             +
     useful_life_months integer,                                                               +
     company_id bigint,                                                                        +
     branch_id bigint,                                                                         +
     current_employee_id bigint,                                                               +
     status varchar(30) NOT NULL DEFAULT 'ACTIVE'::character varying,                          +
     tag_id varchar(100),                                                                      +
     disposable boolean NOT NULL DEFAULT false,                                                +
     deleted_at timestamptz,                                                                   +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now()                                             +
 );                                                                                            +
 
 CREATE TABLE attachments (                                                                    +
     id bigint NOT NULL,                                                                       +
     table_name text NOT NULL,                                                                 +
     record_id bigint NOT NULL,                                                                +
     storage_path text NOT NULL,                                                               +
     uploaded_by uuid,                                                                         +
     uploaded_at timestamptz NOT NULL DEFAULT now(),                                           +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE attendance_daily (                                                               +
     id bigint NOT NULL,                                                                       +
     employee_id bigint NOT NULL,                                                              +
     work_date date NOT NULL,                                                                  +
     minutes_late integer NOT NULL DEFAULT 0,                                                  +
     minutes_early integer NOT NULL DEFAULT 0,                                                 +
     status varchar(20),                                                                       +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE attendance_policies (                                                            +
     id bigint NOT NULL,                                                                       +
     name varchar(80) NOT NULL,                                                                +
     description text,                                                                         +
     grace_minutes integer NOT NULL DEFAULT 0,                                                 +
     penalty_rule_id bigint,                                                                   +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE attendance_records (                                                             +
     id bigint NOT NULL,                                                                       +
     employee_id bigint NOT NULL,                                                              +
     timestamp timestamptz NOT NULL,                                                           +
     io_type varchar(10) NOT NULL,                                                             +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE audit_log (                                                                      +
     id bigint NOT NULL,                                                                       +
     table_name varchar(60) NOT NULL,                                                          +
     record_id bigint NOT NULL,                                                                +
     action varchar(10) NOT NULL,                                                              +
     changed_by uuid,                                                                          +
     changed_at timestamptz NOT NULL DEFAULT now(),                                            +
     diff_json jsonb                                                                           +
 );                                                                                            +
 
 CREATE TABLE bank_accounts (                                                                  +
     id bigint NOT NULL DEFAULT nextval('bank_accounts_id_seq'::regclass),                     +
     bank_id bigint NOT NULL,                                                                  +
     company_id bigint NOT NULL,                                                               +
     account_no varchar(34) NOT NULL,                                                          +
     currency_id bigint NOT NULL,                                                              +
     iban varchar(34),                                                                         +
     is_default boolean NOT NULL DEFAULT false,                                                +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE bank_reconciliations (                                                           +
     id bigint NOT NULL,                                                                       +
     bank_account_id bigint NOT NULL,                                                          +
     statement_id bigint NOT NULL,                                                             +
     reconciled_at timestamptz NOT NULL DEFAULT now(),                                         +
     reconciled_by uuid,                                                                       +
     notes varchar(150),                                                                       +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE bank_statement_details (                                                         +
     id bigint NOT NULL,                                                                       +
     statement_id bigint NOT NULL,                                                             +
     txn_date date NOT NULL,                                                                   +
     description varchar(150),                                                                 +
     amount numeric(18,2) NOT NULL,                                                            +
     reference varchar(60),                                                                    +
     matched boolean NOT NULL DEFAULT false,                                                   +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE bank_statement_header (                                                          +
     id bigint NOT NULL,                                                                       +
     bank_account_id bigint NOT NULL,                                                          +
     statement_date date NOT NULL,                                                             +
     opening_balance numeric(18,2) NOT NULL,                                                   +
     closing_balance numeric(18,2) NOT NULL,                                                   +
     status varchar(20) NOT NULL DEFAULT 'open'::character varying,                            +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE banks (                                                                          +
     id bigint NOT NULL DEFAULT nextval('banks_id_seq'::regclass),                             +
     name varchar(120) NOT NULL,                                                               +
     swift_code varchar(15),                                                                   +
     address text,                                                                             +
     city_id bigint,                                                                           +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE bin_types (                                                                      +
     id bigint NOT NULL DEFAULT nextval('bin_types_id_seq'::regclass),                         +
     code varchar(20) NOT NULL,                                                                +
     name varchar(100) NOT NULL,                                                               +
     description text,                                                                         +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE branch_closures (                                                                +
     id bigint NOT NULL DEFAULT nextval('branch_closures_id_seq'::regclass),                   +
     branch_id bigint NOT NULL,                                                                +
     closure_date date NOT NULL,                                                               +
     reason text,                                                                              +
     closed_by_user_id uuid,                                                                   +
     notes text,                                                                               +
     reopening_date date,                                                                      +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now()                                             +
 );                                                                                            +
 
 CREATE TABLE branch_credit_limits (                                                           +
     branch_id bigint NOT NULL,                                                                +
     limit_type varchar(40) NOT NULL,                                                          +
     value numeric(18,2),                                                                      +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE branch_manager_distributor (                                                     +
     branch_manager_profile_id uuid NOT NULL,                                                  +
     distributor_profile_id uuid NOT NULL                                                      +
 );                                                                                            +
 
 CREATE TABLE branches (                                                                       +
     id bigint NOT NULL DEFAULT nextval('branches_id_seq'::regclass),                          +
     company_id bigint NOT NULL,                                                               +
     branch_code varchar(20) NOT NULL,                                                         +
     name varchar(120) NOT NULL,                                                               +
     city_id bigint,                                                                           +
     address text,                                                                             +
     phone varchar(30),                                                                        +
     email varchar(120),                                                                       +
     is_main boolean NOT NULL DEFAULT false,                                                   +
     status varchar(20) NOT NULL DEFAULT 'ACTIVE'::character varying,                          +
     company_section_id bigint,                                                                +
     deleted_at timestamptz,                                                                   +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now()                                             +
 );                                                                                            +
 
 CREATE TABLE budget_versions (                                                                +
     id bigint NOT NULL,                                                                       +
     budget_id bigint NOT NULL,                                                                +
     version_number smallint NOT NULL,                                                         +
     amount numeric(18,2) NOT NULL,                                                            +
     change_reason text,                                                                       +
     currency_id bigint NOT NULL,                                                              +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE budgets (                                                                        +
     id bigint NOT NULL,                                                                       +
     company_id bigint NOT NULL,                                                               +
     org_unit_id bigint NOT NULL,                                                              +
     fiscal_year smallint NOT NULL,                                                            +
     amount numeric(18,2) NOT NULL,                                                            +
     currency_id bigint NOT NULL,                                                              +
     status varchar(20) NOT NULL DEFAULT 'draft'::character varying,                           +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE business_types (                                                                 +
     id bigint NOT NULL DEFAULT nextval('business_types_id_seq'::regclass),                    +
     name varchar(100) NOT NULL,                                                               +
     description text,                                                                         +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE cash_flow_categories (                                                           +
     id smallint NOT NULL DEFAULT nextval('cash_flow_categories_id_seq'::regclass),            +
     name varchar(60) NOT NULL,                                                                +
     cf_section varchar(12) NOT NULL,                                                          +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE cashier_sessions (                                                               +
     id bigint NOT NULL DEFAULT nextval('cashier_sessions_id_seq'::regclass),                  +
     branch_id bigint NOT NULL,                                                                +
     company_id bigint NOT NULL,                                                               +
     cashier_profile_id uuid NOT NULL,                                                         +
     pos_terminal_id bigint,                                                                   +
     session_start_at timestamptz NOT NULL DEFAULT now(),                                      +
     session_end_at timestamptz,                                                               +
     opening_balance_cash numeric(18,2) NOT NULL DEFAULT 0,                                    +
     closing_balance_cash numeric(18,2),                                                       +
     total_cash_sales numeric(18,2) DEFAULT 0,                                                 +
     total_cash_receipts numeric(18,2) DEFAULT 0,                                              +
     total_cash_payments numeric(18,2) DEFAULT 0,                                              +
     expected_cash_in_drawer numeric(18,2),                                                    +
     actual_cash_in_drawer numeric(18,2),                                                      +
     cash_difference numeric(18,2),                                                            +
     status varchar(20) NOT NULL DEFAULT 'OPEN'::character varying,                            +
     notes text,                                                                               +
     closed_by_profile_id uuid,                                                                +
     reconciled_by_profile_id uuid,                                                            +
     reconciled_at timestamptz,                                                                +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE cheque_books (                                                                   +
     id bigint NOT NULL,                                                                       +
     bank_account_id bigint NOT NULL,                                                          +
     book_number varchar(30) NOT NULL,                                                         +
     first_number integer NOT NULL,                                                            +
     last_number integer NOT NULL,                                                             +
     issue_date date NOT NULL,                                                                 +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE cheques (                                                                        +
     id bigint NOT NULL,                                                                       +
     cheque_book_id bigint NOT NULL,                                                           +
     cheque_number varchar(30) NOT NULL,                                                       +
     amount numeric(18,2) NOT NULL,                                                            +
     payee_type varchar(20),                                                                   +
     payee_id bigint,                                                                          +
     issue_date date,                                                                          +
     due_date date,                                                                            +
     status varchar(20),                                                                       +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE cities (                                                                         +
     id bigint NOT NULL DEFAULT nextval('cities_id_seq'::regclass),                            +
     country_id bigint NOT NULL,                                                               +
     name varchar(100) NOT NULL,                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE combo_definition_details (                                                       +
     id bigint NOT NULL,                                                                       +
     combo_definition_id bigint NOT NULL,                                                      +
     item_id bigint NOT NULL,                                                                  +
     quantity numeric(18,2) NOT NULL,                                                          +
     unit_id bigint NOT NULL,                                                                  +
     price_in_combo numeric(18,4),                                                             +
     discount_percentage_in_combo numeric(5,2) DEFAULT 0,                                      +
     discount_amount_in_combo numeric(18,2) DEFAULT 0,                                         +
     notes text,                                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz,                                                                   +
     created_by uuid,                                                                          +
     updated_by uuid                                                                           +
 );                                                                                            +
 
 CREATE TABLE combo_definitions_header (                                                       +
     id bigint NOT NULL,                                                                       +
     code varchar(50) NOT NULL,                                                                +
     name varchar(150) NOT NULL,                                                               +
     description text,                                                                         +
     company_id bigint NOT NULL,                                                               +
     branch_id bigint,                                                                         +
     price_type varchar(50),                                                                   +
     combo_type combo_type_enum NOT NULL DEFAULT 'manual'::combo_type_enum,                    +
     valid_from timestamptz,                                                                   +
     valid_to timestamptz,                                                                     +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz,                                                                   +
     created_by uuid,                                                                          +
     updated_by uuid                                                                           +
 );                                                                                            +
 
 CREATE TABLE companies (                                                                      +
     id bigint NOT NULL DEFAULT nextval('companies_id_seq'::regclass),                         +
     group_id bigint,                                                                          +
     company_code varchar(20) NOT NULL,                                                        +
     name varchar(120) NOT NULL,                                                               +
     legal_name varchar(200),                                                                  +
     tax_id varchar(50),                                                                       +
     address text,                                                                             +
     base_currency_id bigint NOT NULL,                                                         +
     phone varchar(30),                                                                        +
     email varchar(120),                                                                       +
     status varchar(20) NOT NULL DEFAULT 'ACTIVE'::character varying,                          +
     deleted_at timestamptz,                                                                   +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     primary_section_id bigint,                                                                +
     business_type_id bigint                                                                   +
 );                                                                                            +
 
 CREATE TABLE company_customer_balances (                                                      +
     id bigint NOT NULL DEFAULT nextval('company_customer_balances_id_seq'::regclass),         +
     company_id bigint NOT NULL,                                                               +
     customer_id bigint NOT NULL,                                                              +
     balance numeric(18,2) NOT NULL DEFAULT 0,                                                 +
     last_updated_at timestamptz NOT NULL DEFAULT now(),                                       +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE company_customer_credit_limits (                                                 +
     id bigint NOT NULL DEFAULT nextval('company_customer_credit_limits_id_seq'::regclass),    +
     company_id bigint NOT NULL,                                                               +
     customer_id bigint NOT NULL,                                                              +
     credit_limit numeric(18,2) NOT NULL DEFAULT 0,                                            +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE company_groups (                                                                 +
     id bigint NOT NULL DEFAULT nextval('company_groups_id_seq'::regclass),                    +
     group_code varchar(20) NOT NULL,                                                          +
     name varchar(120) NOT NULL,                                                               +
     description text,                                                                         +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE company_sections (                                                               +
     id bigint NOT NULL DEFAULT nextval('company_sections_id_seq'::regclass),                  +
     company_id bigint NOT NULL,                                                               +
     section_code varchar(30) NOT NULL,                                                        +
     name varchar(120) NOT NULL,                                                               +
     description text,                                                                         +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     created_by uuid,                                                                          +
     updated_by uuid,                                                                          +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE company_supplier_assignments (                                                   +
     company_id bigint NOT NULL,                                                               +
     supplier_id bigint NOT NULL,                                                              +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE cost_centers (                                                                   +
     id bigint NOT NULL DEFAULT nextval('cost_centers_id_seq'::regclass),                      +
     company_id bigint NOT NULL,                                                               +
     code varchar(12) NOT NULL,                                                                +
     name varchar(120) NOT NULL,                                                               +
     parent_id bigint,                                                                         +
     level_no smallint NOT NULL DEFAULT 1,                                                     +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE countries (                                                                      +
     id bigint NOT NULL DEFAULT nextval('countries_id_seq'::regclass),                         +
     country_code character NOT NULL,                                                          +
     name varchar(100) NOT NULL,                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE crm_activities (                                                                 +
     id bigint NOT NULL,                                                                       +
     activity_type varchar(20) NOT NULL,                                                       +
     activity_date timestamptz NOT NULL,                                                       +
     status text,                                                                              +
     company_id bigint NOT NULL,                                                               +
     contact_person text,                                                                      +
     meeting_duration_minutes bigint,                                                          +
     opportunity_id bigint,                                                                    +
     lead_id bigint,                                                                           +
     notes text,                                                                               +
     created_by uuid,                                                                          +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE crm_leads (                                                                      +
     id bigint NOT NULL,                                                                       +
     lead_name varchar(100) NOT NULL,                                                          +
     company_name varchar(100) NOT NULL,                                                       +
     company_id bigint NOT NULL,                                                               +
     email varchar(100),                                                                       +
     lead_rating bigint,                                                                       +
     phone_number varchar(100),                                                                +
     note text,                                                                                +
     source varchar(50),                                                                       +
     status varchar(20) NOT NULL DEFAULT 'new'::character varying,                             +
     assigned_to uuid,                                                                         +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE crm_opportunities (                                                              +
     id bigint NOT NULL,                                                                       +
     opportunity_name varchar(100) NOT NULL,                                                   +
     lead_id bigint,                                                                           +
     customer_id bigint,                                                                       +
     company_id bigint NOT NULL,                                                               +
     stage varchar(50) NOT NULL,                                                               +
     note text,                                                                                +
     probability bigint,                                                                       +
     status text,                                                                              +
     expected_close_date date,                                                                 +
     amount numeric(18,2),                                                                     +
     closed_date date,                                                                         +
     expected_revenue bigint,                                                                  +
     assigned_to uuid,                                                                         +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE currencies (                                                                     +
     id bigint NOT NULL DEFAULT nextval('currencies_id_seq'::regclass),                        +
     iso_code character NOT NULL,                                                              +
     symbol varchar(10),                                                                       +
     name varchar(50) NOT NULL,                                                                +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE currency_rates (                                                                 +
     id bigint NOT NULL DEFAULT nextval('currency_rates_id_seq'::regclass),                    +
     currency_id bigint NOT NULL,                                                              +
     rate_date date NOT NULL,                                                                  +
     rate numeric(18,6) NOT NULL,                                                              +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE custom_regions (                                                                 +
     id bigint NOT NULL DEFAULT nextval('custom_regions_id_seq'::regclass),                    +
     name varchar(100) NOT NULL,                                                               +
     description text,                                                                         +
     parent_region_id bigint,                                                                  +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE customer_area_assignments (                                                      +
     id bigint NOT NULL,                                                                       +
     customer_id bigint NOT NULL,                                                              +
     sales_area_id bigint NOT NULL,                                                            +
     created_at timestamptz DEFAULT now(),                                                     +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE customer_category (                                                              +
     id bigint NOT NULL,                                                                       +
     name varchar(120) NOT NULL,                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE customer_credit_limits (                                                         +
     customer_id bigint NOT NULL,                                                              +
     currency_id bigint NOT NULL,                                                              +
     credit_limit numeric(18,2),                                                               +
     outstanding numeric(18,2),                                                                +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE customer_credit_notes (                                                          +
     id bigint NOT NULL,                                                                       +
     customer_id bigint NOT NULL,                                                              +
     invoice_id bigint,                                                                        +
     credit_note_number varchar(50) NOT NULL,                                                  +
     credit_note_date date NOT NULL,                                                           +
     amount numeric(18,2) NOT NULL,                                                            +
     reason varchar(150),                                                                      +
     status varchar(20) NOT NULL DEFAULT 'open'::character varying,                            +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE customer_installment_plans (                                                     +
     id bigint NOT NULL,                                                                       +
     invoice_id bigint,                                                                        +
     customer_id bigint NOT NULL,                                                              +
     total_amount numeric(18,2) NOT NULL,                                                      +
     start_date date NOT NULL,                                                                 +
     installments_count smallint NOT NULL,                                                     +
     status varchar(20) NOT NULL DEFAULT 'active'::character varying,                          +
     created_at timestamptz DEFAULT now(),                                                     +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE customer_installments (                                                          +
     id bigint NOT NULL,                                                                       +
     plan_id bigint NOT NULL,                                                                  +
     installment_number smallint NOT NULL,                                                     +
     due_date date NOT NULL,                                                                   +
     amount numeric(18,2) NOT NULL,                                                            +
     paid_date date,                                                                           +
     payment_id bigint,                                                                        +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE customer_receipts (                                                              +
     id bigint NOT NULL,                                                                       +
     customer_id bigint NOT NULL,                                                              +
     company_id bigint NOT NULL,                                                               +
     branch_id bigint,                                                                         +
     receipt_number varchar(50) NOT NULL,                                                      +
     receipt_date date NOT NULL,                                                               +
     amount numeric(18,2) NOT NULL,                                                            +
     payment_mode varchar(20) NOT NULL,                                                        +
     cheque_number varchar(30),                                                                +
     bank_account_id bigint,                                                                   +
     reference varchar(60),                                                                    +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     is_cancelled boolean NOT NULL DEFAULT false,                                              +
     cancelled_reason varchar(100),                                                            +
     deleted_at timestamptz,                                                                   +
     cashier_session_id bigint                                                                 +
 );                                                                                            +
 
 CREATE TABLE customer_sales_targets (                                                         +
     id bigint NOT NULL DEFAULT nextval('customer_sales_targets_id_seq'::regclass),            +
     customer_id bigint NOT NULL,                                                              +
     target_year smallint NOT NULL,                                                            +
     target_month smallint,                                                                    +
     target_quarter smallint,                                                                  +
     item_id bigint,                                                                           +
     item_group_id bigint,                                                                     +
     target_quantity numeric(18,2),                                                            +
     notes text,                                                                               +
     created_by_profile_id uuid,                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE customers (                                                                      +
     id bigint NOT NULL,                                                                       +
     branch_id bigint,                                                                         +
     code varchar(30) NOT NULL,                                                                +
     name varchar(150) NOT NULL,                                                               +
     group_id bigint,                                                                          +
     city_id bigint,                                                                           +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     tax_exempt boolean NOT NULL DEFAULT false,                                                +
     tax_exempt_reason text,                                                                   +
     created_at timestamptz DEFAULT now(),                                                     +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE cycle_count_results (                                                            +
     id bigint NOT NULL,                                                                       +
     cycle_count_id bigint NOT NULL,                                                           +
     item_id bigint NOT NULL,                                                                  +
     counted_quantity numeric(18,2) NOT NULL,                                                  +
     system_quantity numeric(18,2) NOT NULL,                                                   +
     variance numeric(18,2) NOT NULL,                                                          +
     notes text,                                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE cycle_counts (                                                                   +
     id bigint NOT NULL,                                                                       +
     warehouse_id bigint NOT NULL,                                                             +
     count_date date NOT NULL,                                                                 +
     status varchar(20) NOT NULL DEFAULT 'draft'::character varying,                           +
     counted_by uuid,                                                                          +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE deduction_types (                                                                +
     id bigint NOT NULL,                                                                       +
     code varchar(20) NOT NULL,                                                                +
     name varchar(80) NOT NULL,                                                                +
     description text,                                                                         +
     statutory boolean NOT NULL DEFAULT false,                                                 +
     gl_account_id bigint,                                                                     +
     company_id bigint,                                                                        +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE delivery_header (                                                                +
     id bigint NOT NULL,                                                                       +
     order_id bigint NOT NULL,                                                                 +
     delivery_number varchar(50) NOT NULL,                                                     +
     branch_id bigint,                                                                         +
     delivery_address text,                                                                    +
     driver_id uuid,                                                                           +
     note text,                                                                                +
     delivery_date date NOT NULL,                                                              +
     status varchar(20) NOT NULL DEFAULT 'draft'::character varying,                           +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz,                                                                   +
     shipping_method_id bigint                                                                 +
 );                                                                                            +
 
 CREATE TABLE departments (                                                                    +
     id bigint NOT NULL DEFAULT nextval('departments_id_seq'::regclass),                       +
     branch_id bigint,                                                                         +
     name varchar(120) NOT NULL,                                                               +
     parent_department_id bigint,                                                              +
     status varchar(20) NOT NULL DEFAULT 'ACTIVE'::character varying,                          +
     deleted_at timestamptz,                                                                   +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now()                                             +
 );                                                                                            +
 
 CREATE TABLE doctor_class (                                                                   +
     id bigint NOT NULL DEFAULT nextval('doctor_class_id_seq'::regclass),                      +
     code varchar(50),                                                                         +
     name_ar varchar(100) NOT NULL,                                                            +
     name_en varchar(100) NOT NULL,                                                            +
     notes text,                                                                               +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     company_id bigint,                                                                        +
     created_by uuid,                                                                          +
     updated_by uuid,                                                                          +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE doctor_marketing_areas (                                                         +
     id bigint NOT NULL DEFAULT nextval('doctor_marketing_areas_id_seq'::regclass),            +
     company_id bigint NOT NULL,                                                               +
     code varchar(50),                                                                         +
     name varchar(100) NOT NULL,                                                               +
     notes text,                                                                               +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     created_by uuid,                                                                          +
     updated_by uuid,                                                                          +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE doctor_qualifications (                                                          +
     id bigint NOT NULL DEFAULT nextval('doctor_qualifications_id_seq'::regclass),             +
     code varchar(50),                                                                         +
     name_ar varchar(255) NOT NULL,                                                            +
     name_en varchar(255) NOT NULL,                                                            +
     notes text,                                                                               +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     company_id bigint,                                                                        +
     created_by uuid,                                                                          +
     updated_by uuid,                                                                          +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE doctor_specialties (                                                             +
     id bigint NOT NULL DEFAULT nextval('doctor_specialties_id_seq'::regclass),                +
     code varchar(50),                                                                         +
     name_ar varchar(255) NOT NULL,                                                            +
     name_en varchar(255) NOT NULL,                                                            +
     notes text,                                                                               +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     company_id bigint,                                                                        +
     created_by uuid,                                                                          +
     updated_by uuid,                                                                          +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE doctors (                                                                        +
     id bigint NOT NULL DEFAULT nextval('doctors_id_seq'::regclass),                           +
     company_id bigint NOT NULL,                                                               +
     branch_id bigint,                                                                         +
     doctor_code varchar(50) NOT NULL,                                                         +
     full_name varchar(255) NOT NULL,                                                          +
     last_name varchar(120),                                                                   +
     specialty_id bigint,                                                                      +
     qualification_id bigint,                                                                  +
     class_id bigint,                                                                          +
     marketing_area_id bigint,                                                                 +
     city_id bigint,                                                                           +
     address_line1 text,                                                                       +
     address_line2 text,                                                                       +
     mobile_phone varchar(30),                                                                 +
     email varchar(120),                                                                       +
     gender varchar(10),                                                                       +
     work_status varchar(50),                                                                  +
     primary_workplace_name text,                                                              +
     secondary_workplace_name text,                                                            +
     marketing_area_detail1 text,                                                              +
     marketing_area_detail2 text,                                                              +
     primary_associated_pharmacy text,                                                         +
     secondary_associated_pharmacy text,                                                       +
     notes text,                                                                               +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     primary_medical_rep_id bigint,                                                            +
     created_by uuid,                                                                          +
     updated_by uuid,                                                                          +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE earning_types (                                                                  +
     id bigint NOT NULL,                                                                       +
     code varchar(20) NOT NULL,                                                                +
     name varchar(120) NOT NULL,                                                               +
     description text,                                                                         +
     taxable boolean NOT NULL DEFAULT true,                                                    +
     is_recurring boolean NOT NULL DEFAULT true,                                               +
     gl_account_id bigint,                                                                     +
     company_id bigint,                                                                        +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE employee_allowances (                                                            +
     id bigint NOT NULL,                                                                       +
     employee_id bigint NOT NULL,                                                              +
     allowance_type_id bigint NOT NULL,                                                        +
     amount numeric(18,2) NOT NULL,                                                            +
     currency_id bigint,                                                                       +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     notes text,                                                                               +
     start_date date,                                                                          +
     end_date date,                                                                            +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE employee_assignments (                                                           +
     id bigint NOT NULL DEFAULT nextval('employee_assignments_id_seq'::regclass),              +
     employee_id bigint NOT NULL,                                                              +
     company_id bigint NOT NULL,                                                               +
     branch_id bigint,                                                                         +
     department_id bigint,                                                                     +
     position_role_id bigint,                                                                  +
     supervisor_id bigint,                                                                     +
     start_date date NOT NULL,                                                                 +
     end_date date,                                                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE employee_expense_claims_details (                                                +
     id bigint NOT NULL DEFAULT nextval('employee_expense_claims_details_id_seq'::regclass),   +
     claim_header_id bigint NOT NULL,                                                          +
     expense_date date NOT NULL,                                                               +
     expense_type_id bigint,                                                                   +
     description varchar(255) NOT NULL,                                                        +
     claimed_amount numeric(18,2) NOT NULL,                                                    +
     approved_amount numeric(18,2) DEFAULT 0,                                                  +
     receipt_attachment_id bigint,                                                             +
     notes text,                                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE employee_expense_claims_header (                                                 +
     id bigint NOT NULL DEFAULT nextval('employee_expense_claims_header_id_seq'::regclass),    +
     employee_id bigint NOT NULL,                                                              +
     claim_number varchar(30) NOT NULL,                                                        +
     claim_date date NOT NULL,                                                                 +
     description text,                                                                         +
     total_claimed_amount numeric(18,2) NOT NULL DEFAULT 0,                                    +
     total_approved_amount numeric(18,2) DEFAULT 0,                                            +
     status varchar(30) NOT NULL DEFAULT 'DRAFT'::character varying,                           +
     approved_by_profile_id uuid,                                                              +
     approval_date date,                                                                       +
     paid_date date,                                                                           +
     payment_reference varchar(100),                                                           +
     attachment_url text,                                                                      +
     created_by_profile_id uuid,                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE employee_fingerprint_logs (                                                      +
     id bigint NOT NULL DEFAULT nextval('employee_fingerprint_logs_id_seq'::regclass),         +
     employee_id bigint NOT NULL,                                                              +
     branch_id bigint NOT NULL,                                                                +
     fingerprint_time timestamptz NOT NULL,                                                    +
     log_type varchar(3) NOT NULL,                                                             +
     device_id varchar(100),                                                                   +
     notes text,                                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE employee_insurances (                                                            +
     id bigint NOT NULL,                                                                       +
     employee_id bigint NOT NULL,                                                              +
     policy_number varchar(60),                                                                +
     provider varchar(120),                                                                    +
     start_date date,                                                                          +
     end_date date,                                                                            +
     premium numeric(18,2),                                                                    +
     coverage_amount numeric(18,2),                                                            +
     employer_share numeric(18,2),                                                             +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE employee_loan_installments (                                                     +
     id bigint NOT NULL,                                                                       +
     loan_id bigint NOT NULL,                                                                  +
     due_date date NOT NULL,                                                                   +
     amount numeric(14,2) NOT NULL,                                                            +
     paid_date date,                                                                           +
     status varchar(20) DEFAULT 'PENDING'::character varying,                                  +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE employee_loans (                                                                 +
     id bigint NOT NULL,                                                                       +
     employee_id bigint NOT NULL,                                                              +
     original_amount numeric(14,2) NOT NULL,                                                   +
     approved_amount numeric(14,2),                                                            +
     currency_id bigint,                                                                       +
     start_date date NOT NULL,                                                                 +
     interest_rate numeric(5,2) DEFAULT 0,                                                     +
     term_months integer NOT NULL,                                                             +
     status varchar(20) DEFAULT 'ACTIVE'::character varying,                                   +
     approved_by uuid,                                                                         +
     approved_at timestamptz,                                                                  +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE employee_qualifications (                                                        +
     id bigint NOT NULL DEFAULT nextval('employee_qualifications_id_seq'::regclass),           +
     employee_id bigint NOT NULL,                                                              +
     qualification_type_id bigint NOT NULL,                                                    +
     institution varchar(120),                                                                 +
     awarded_date date,                                                                        +
     expires_at date,                                                                          +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE employee_retributions (                                                          +
     id bigint NOT NULL,                                                                       +
     employee_id bigint NOT NULL,                                                              +
     retribution_type_id bigint NOT NULL,                                                      +
     amount numeric(18,2) NOT NULL,                                                            +
     date date NOT NULL,                                                                       +
     notes text,                                                                               +
     is_settled boolean NOT NULL DEFAULT false,                                                +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE employee_sales_book_assignments (                                                +
     id bigint NOT NULL DEFAULT nextval('employee_sales_book_assignments_id_seq'::regclass),   +
     employee_id bigint NOT NULL,                                                              +
     sales_book_id bigint NOT NULL,                                                            +
     assigned_from_number integer,                                                             +
     assigned_to_number integer,                                                               +
     assignment_date date NOT NULL,                                                            +
     status text,                                                                              +
     return_date date,                                                                         +
     notes text,                                                                               +
     created_by_profile_id uuid,                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE employee_tax_profiles (                                                          +
     id bigint NOT NULL,                                                                       +
     employee_id bigint NOT NULL,                                                              +
     tax_code varchar(50),                                                                     +
     effective_from date,                                                                      +
     basis varchar(50),                                                                        +
     ninsur_rate numeric(5,2),                                                                 +
     tax_exempt_till date,                                                                     +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE employee_types (                                                                 +
     id bigint NOT NULL DEFAULT nextval('employee_types_id_seq'::regclass),                    +
     name varchar(100) NOT NULL,                                                               +
     description text,                                                                         +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE employees (                                                                      +
     id bigint NOT NULL DEFAULT nextval('employees_id_seq'::regclass),                         +
     employee_code varchar(20) NOT NULL,                                                       +
     first_name varchar(60) NOT NULL,                                                          +
     last_name varchar(60) NOT NULL,                                                           +
     gender varchar(10),                                                                       +
     date_of_birth date,                                                                       +
     hire_date date,                                                                           +
     address_line1 varchar(255),                                                               +
     address_line2 varchar(255),                                                               +
     city_id bigint,                                                                           +
     postal_code varchar(20),                                                                  +
     phone varchar(30),                                                                        +
     personal_email varchar(120),                                                              +
     company_id bigint NOT NULL,                                                               +
     branch_id bigint,                                                                         +
     national_id_number varchar(50),                                                           +
     passport_number varchar(50),                                                              +
     guarantor jsonb,                                                                          +
     nationality varchar(100),                                                                 +
     status varchar(20) NOT NULL DEFAULT 'ACTIVE'::character varying,                          +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz,                                                                   +
     employee_type_id bigint,                                                                  +
     department_id bigint                                                                      +
 );                                                                                            +
 
 CREATE TABLE encumbrances (                                                                   +
     id bigint NOT NULL,                                                                       +
     budget_id bigint NOT NULL,                                                                +
     account_id bigint NOT NULL,                                                               +
     amount numeric(18,2) NOT NULL,                                                            +
     encumbrance_date date NOT NULL,                                                           +
     status varchar(20) NOT NULL DEFAULT 'open'::character varying,                            +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE expense_types (                                                                  +
     id bigint NOT NULL DEFAULT nextval('expense_types_id_seq'::regclass),                     +
     name varchar(100) NOT NULL,                                                               +
     description text,                                                                         +
     default_gl_account_id bigint,                                                             +
     requires_receipt boolean NOT NULL DEFAULT true,                                           +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE gl_journal_details (                                                             +
     id bigint NOT NULL DEFAULT nextval('gl_journal_details_id_seq'::regclass),                +
     journal_id bigint NOT NULL,                                                               +
     line_no smallint NOT NULL,                                                                +
     account_id bigint NOT NULL,                                                               +
     description text,                                                                         +
     debit numeric(18,2) NOT NULL DEFAULT 0,                                                   +
     credit numeric(18,2) NOT NULL DEFAULT 0,                                                  +
     cost_center_id bigint,                                                                    +
     currency_id bigint,                                                                       +
     exchange_rate numeric(18,6),                                                              +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE gl_journal_header (                                                              +
     id bigint NOT NULL DEFAULT nextval('gl_journal_header_id_seq'::regclass),                 +
     voucher_no varchar(30) NOT NULL,                                                          +
     voucher_type_id bigint NOT NULL,                                                          +
     description text,                                                                         +
     journal_date date NOT NULL,                                                               +
     company_id bigint NOT NULL,                                                               +
     branch_id bigint,                                                                         +
     posted_by uuid,                                                                           +
     posted_at timestamptz,                                                                    +
     status_id smallint NOT NULL,                                                              +
     deleted_at timestamptz,                                                                   +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now()                                             +
 );                                                                                            +
 
 CREATE TABLE goods_received_note_details (                                                    +
     id bigint NOT NULL,                                                                       +
     grn_header_id bigint NOT NULL,                                                            +
     line_number smallint NOT NULL,                                                            +
     item_id bigint NOT NULL,                                                                  +
     unit_id bigint NOT NULL,                                                                  +
     quantity_ordered numeric(18,2) NOT NULL,                                                  +
     quantity_received numeric(18,2) NOT NULL,                                                 +
     unit_price numeric(18,4) NOT NULL,                                                        +
     total numeric(18,2) NOT NULL,                                                             +
     batch_number varchar(40),                                                                 +
     expiry_date date,                                                                         +
     notes text,                                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE goods_received_note_header (                                                     +
     id bigint NOT NULL,                                                                       +
     purchase_order_id bigint,                                                                 +
     grn_number varchar(50) NOT NULL,                                                          +
     grn_date date NOT NULL,                                                                   +
     supplier_id bigint,                                                                       +
     supplier_invoice_number varchar(40),                                                      +
     lead_time_in_days integer,                                                                +
     total_amount numeric(18,2) NOT NULL,                                                      +
     status varchar(20) NOT NULL DEFAULT 'received'::character varying,                        +
     branch_id bigint,                                                                         +
     delivered_by varchar(150),                                                                +
     delivery_note_number varchar(50),                                                         +
     received_by_id uuid,                                                                      +
     invoice_date date,                                                                        +
     currency_id bigint,                                                                       +
     created_at timestamptz DEFAULT now(),                                                     +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz,                                                                   +
     created_by uuid                                                                           +
 );                                                                                            +
 
 CREATE TABLE hr_job_roles (                                                                   +
     id bigint NOT NULL DEFAULT nextval('hr_job_roles_id_seq'::regclass),                      +
     code varchar(20) NOT NULL,                                                                +
     title varchar(100) NOT NULL,                                                              +
     description text,                                                                         +
     responsibilities text,                                                                    +
     department_id bigint,                                                                     +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE incentive_types (                                                                +
     id bigint NOT NULL,                                                                       +
     code varchar(20) NOT NULL,                                                                +
     name varchar(120) NOT NULL,                                                               +
     description text,                                                                         +
     taxable boolean NOT NULL DEFAULT true,                                                    +
     is_recurring boolean NOT NULL DEFAULT true,                                               +
     gl_account_id bigint,                                                                     +
     company_id bigint,                                                                        +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE invoice_details (                                                                +
     id bigint NOT NULL,                                                                       +
     invoice_id bigint NOT NULL,                                                               +
     item_id bigint NOT NULL,                                                                  +
     quantity numeric(18,2) NOT NULL,                                                          +
     description varchar(150),                                                                 +
     unit_price numeric(18,4) NOT NULL,                                                        +
     discount numeric(18,2) DEFAULT 0,                                                         +
     tax_amount numeric(18,2) DEFAULT 0,                                                       +
     total numeric(18,2) NOT NULL,                                                             +
     unit_id bigint,                                                                           +
     combo_definition_id bigint,                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE invoice_header (                                                                 +
     id bigint NOT NULL,                                                                       +
     branch_id bigint,                                                                         +
     company_id bigint NOT NULL,                                                               +
     customer_id bigint NOT NULL,                                                              +
     invoice_number varchar(50) NOT NULL,                                                      +
     invoice_date date NOT NULL,                                                               +
     invoice_type_id bigint NOT NULL,                                                          +
     billing_city_id bigint,                                                                   +
     shipping_city_id bigint,                                                                  +
     total_amount numeric(18,2) NOT NULL,                                                      +
     discount_amount numeric(18,2) NOT NULL DEFAULT 0,                                         +
     tax_amount numeric(18,2) NOT NULL DEFAULT 0,                                              +
     status varchar(20) NOT NULL DEFAULT 'open'::character varying,                            +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz,                                                                   +
     sales_type_id bigint,                                                                     +
     sales_coupon_code_id bigint,                                                              +
     due_date date                                                                             +
 );                                                                                            +
 
 CREATE TABLE invoice_tax_details (                                                            +
     id bigint NOT NULL,                                                                       +
     invoice_line_id bigint NOT NULL,                                                          +
     tax_code_id bigint NOT NULL,                                                              +
     tax_rate_id bigint,                                                                       +
     tax_amount numeric(18,2) NOT NULL,                                                        +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE invoice_types (                                                                  +
     id bigint NOT NULL,                                                                       +
     name varchar(80) NOT NULL,                                                                +
     due_days integer NOT NULL DEFAULT 0,                                                      +
     description text,                                                                         +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE item_cost_adjustments (                                                          +
     id bigint NOT NULL DEFAULT nextval('item_cost_adjustments_id_seq'::regclass),             +
     item_id bigint NOT NULL,                                                                  +
     adjustment_date date NOT NULL,                                                            +
     old_cost numeric(18,4),                                                                   +
     new_cost numeric(18,4) NOT NULL,                                                          +
     quantity_affected numeric(18,2),                                                          +
     warehouse_id bigint,                                                                      +
     reason text NOT NULL,                                                                     +
     approved_by_profile_id uuid,                                                              +
     created_by_profile_id uuid,                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE item_cost_history (                                                              +
     id bigint NOT NULL DEFAULT nextval('item_cost_history_id_seq'::regclass),                 +
     item_id bigint NOT NULL,                                                                  +
     effective_date date NOT NULL,                                                             +
     cost numeric(18,4) NOT NULL,                                                              +
     reason varchar(255),                                                                      +
     source_document_type varchar(50),                                                         +
     source_document_id bigint,                                                                +
     created_by_profile_id uuid,                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE item_main_groups (                                                               +
     id bigint NOT NULL,                                                                       +
     company_id bigint NOT NULL,                                                               +
     code varchar(30) NOT NULL,                                                                +
     name varchar(120) NOT NULL,                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz,                                                                   +
     created_by uuid,                                                                          +
     updated_by uuid                                                                           +
 );                                                                                            +
 
 CREATE TABLE item_serials (                                                                   +
     id bigint NOT NULL,                                                                       +
     item_id bigint NOT NULL,                                                                  +
     serial_number varchar(100) NOT NULL,                                                      +
     status varchar(20) NOT NULL DEFAULT 'available'::character varying,                       +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE item_sub_groups (                                                                +
     id bigint NOT NULL,                                                                       +
     item_main_group_id bigint NOT NULL,                                                       +
     company_id bigint NOT NULL,                                                               +
     code varchar(30) NOT NULL,                                                                +
     name varchar(120) NOT NULL,                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz,                                                                   +
     created_by uuid,                                                                          +
     updated_by uuid                                                                           +
 );                                                                                            +
 
 CREATE TABLE item_types (                                                                     +
     id bigint NOT NULL DEFAULT nextval('item_types_id_seq'::regclass),                        +
     name varchar(100) NOT NULL,                                                               +
     description text,                                                                         +
     is_stockable boolean NOT NULL DEFAULT true,                                               +
     is_sellable boolean NOT NULL DEFAULT true,                                                +
     is_purchasable boolean NOT NULL DEFAULT true,                                             +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE item_versions (                                                                  +
     id bigint NOT NULL,                                                                       +
     item_id bigint NOT NULL,                                                                  +
     version_number integer NOT NULL,                                                          +
     code text NOT NULL,                                                                       +
     name text NOT NULL,                                                                       +
     unit_id bigint NOT NULL,                                                                  +
     main_group_id bigint,                                                                     +
     sub_group_id bigint,                                                                      +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     created_by uuid,                                                                          +
     change_reason text,                                                                       +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE items (                                                                          +
     id bigint NOT NULL,                                                                       +
     company_id bigint NOT NULL,                                                               +
     code text NOT NULL,                                                                       +
     name text NOT NULL,                                                                       +
     unit_id bigint NOT NULL,                                                                  +
     main_group_id bigint,                                                                     +
     sub_group_id bigint,                                                                      +
     barcode varchar(100),                                                                     +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     company_section_id bigint,                                                                +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz,                                                                   +
     created_by uuid,                                                                          +
     updated_by uuid,                                                                          +
     item_type_id bigint                                                                       +
 );                                                                                            +
 
 CREATE TABLE leave_requests (                                                                 +
     id bigint NOT NULL DEFAULT nextval('leave_requests_id_seq'::regclass),                    +
     employee_id bigint NOT NULL,                                                              +
     leave_type_id bigint NOT NULL,                                                            +
     start_date date NOT NULL,                                                                 +
     end_date date NOT NULL,                                                                   +
     status varchar(20) NOT NULL DEFAULT 'PENDING'::character varying,                         +
     approved_by bigint,                                                                       +
     approved_at timestamptz,                                                                  +
     deleted_at timestamptz,                                                                   +
     created_at timestamptz NOT NULL DEFAULT now()                                             +
 );                                                                                            +
 
 CREATE TABLE leave_types (                                                                    +
     id bigint NOT NULL DEFAULT nextval('leave_types_id_seq'::regclass),                       +
     code varchar(10) NOT NULL,                                                                +
     name varchar(50) NOT NULL,                                                                +
     paid boolean NOT NULL DEFAULT true,                                                       +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE licence_items (                                                                  +
     id bigint NOT NULL,                                                                       +
     licence_id bigint NOT NULL,                                                               +
     item_id bigint NOT NULL,                                                                  +
     quantity numeric(18,2) NOT NULL,                                                          +
     unit_value numeric(18,4) NOT NULL,                                                        +
     unit_id bigint,                                                                           +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE licences (                                                                       +
     id bigint NOT NULL,                                                                       +
     supplier_id bigint,                                                                       +
     licence_number varchar(30) NOT NULL,                                                      +
     issued_date date NOT NULL,                                                                +
     expiry_date date,                                                                         +
     currency_id bigint,                                                                       +
     total_value numeric(18,2),                                                                +
     branch_id bigint,                                                                         +
     company_id bigint NOT NULL,                                                               +
     issued_by uuid,                                                                           +
     status varchar(20) NOT NULL DEFAULT 'ACTIVE'::character varying,                          +
     linked_to_purchase_order_id bigint,                                                       +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE marketing_services_request_details (                                             +
     id bigint NOT NULL DEFAULT nextval('marketing_services_request_details_id_seq'::regclass),+
     request_header_id bigint NOT NULL,                                                        +
     detail_type_code varchar(20) NOT NULL,                                                    +
     line_number smallint NOT NULL,                                                            +
     medical_rep_profile_id uuid,                                                              +
     item_id bigint,                                                                           +
     customer_id bigint,                                                                       +
     service_description text,                                                                 +
     quantity numeric(18,2) NOT NULL DEFAULT 0,                                                +
     unit_id bigint,                                                                           +
     notes text,                                                                               +
     status_code varchar(20),                                                                  +
     created_by uuid,                                                                          +
     updated_by uuid,                                                                          +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz,                                                                   +
     legacy_op_t1 varchar(2),                                                                  +
     legacy_op_t2 timestamptz                                                                  +
 );                                                                                            +
 
 CREATE TABLE marketing_services_request_header (                                              +
     id bigint NOT NULL DEFAULT nextval('marketing_services_request_header_id_seq'::regclass), +
     company_id bigint NOT NULL,                                                               +
     branch_id bigint NOT NULL,                                                                +
     request_number varchar(50) NOT NULL,                                                      +
     doctor_id bigint NOT NULL,                                                                +
     request_date timestamptz NOT NULL,                                                        +
     total_amount numeric(18,2) NOT NULL DEFAULT 0,                                            +
     service_kind_code varchar(50),                                                            +
     service_time_code varchar(50),                                                            +
     requester_profile_id uuid,                                                                +
     requester_name_snapshot varchar(120),                                                     +
     purpose_notes text,                                                                       +
     additional_field0 text,                                                                   +
     additional_field1 text,                                                                   +
     additional_field3 text,                                                                   +
     general_notes text,                                                                       +
     status_date0 timestamptz,                                                                 +
     status_date1 timestamptz,                                                                 +
     status_date2 timestamptz,                                                                 +
     status_date3 timestamptz,                                                                 +
     overall_status_date timestamptz,                                                          +
     status_code0 varchar(20),                                                                 +
     status_code1 varchar(20),                                                                 +
     status_code2 varchar(20),                                                                 +
     status_code3 varchar(20),                                                                 +
     overall_status varchar(30) NOT NULL DEFAULT 'DRAFT'::character varying,                   +
     created_by uuid,                                                                          +
     updated_by uuid,                                                                          +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz,                                                                   +
     legacy_op_t1 varchar(2),                                                                  +
     legacy_op_t2 timestamptz                                                                  +
 );                                                                                            +
 
 CREATE TABLE notifications (                                                                  +
     id bigint NOT NULL DEFAULT nextval('notifications_id_seq'::regclass),                     +
     recipient_target_type varchar(10) NOT NULL,                                               +
     recipient_user_id uuid,                                                                   +
     recipient_role_id bigint,                                                                 +
     for_role bigint,                                                                          +
     subject text,                                                                             +
     body text,                                                                                +
     footer text,                                                                              +
     notification_type varchar(20) NOT NULL,                                                   +
     link text,                                                                                +
     status varchar(20) NOT NULL DEFAULT 'NEW'::character varying,                             +
     is_read boolean NOT NULL DEFAULT false,                                                   +
     read_at timestamptz,                                                                      +
     sent_at timestamptz,                                                                      +
     deleted_at timestamptz,                                                                   +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now()                                             +
 );                                                                                            +
 
 CREATE TABLE organization_unit_types (                                                        +
     id bigint NOT NULL DEFAULT nextval('organization_unit_types_id_seq'::regclass),           +
     name varchar(100) NOT NULL,                                                               +
     description text,                                                                         +
     table_source_name varchar(64),                                                            +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE organization_units (                                                             +
     id bigint NOT NULL DEFAULT nextval('organization_units_id_seq'::regclass),                +
     company_id bigint NOT NULL,                                                               +
     org_unit_type_id bigint NOT NULL,                                                         +
     name varchar(150) NOT NULL,                                                               +
     code varchar(50) NOT NULL,                                                                +
     reference_id bigint,                                                                      +
     parent_org_unit_id bigint,                                                                +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE payroll_details (                                                                +
     id bigint NOT NULL,                                                                       +
     payrun_id bigint NOT NULL,                                                                +
     employee_id bigint NOT NULL,                                                              +
     gross numeric(18,2) NOT NULL DEFAULT 0,                                                   +
     allowances numeric(18,2) NOT NULL DEFAULT 0,                                              +
     deductions numeric(18,2) NOT NULL DEFAULT 0,                                              +
     deduction_type_id bigint,                                                                 +
     incentives_amount numeric(18,2) NOT NULL DEFAULT 0,                                       +
     incentive_type_id bigint,                                                                 +
     net numeric(18,2) NOT NULL DEFAULT 0,                                                     +
     comments text,                                                                            +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE payroll_headers (                                                                +
     id bigint NOT NULL,                                                                       +
     period_id bigint NOT NULL,                                                                +
     run_date date NOT NULL,                                                                   +
     status varchar(20) NOT NULL DEFAULT 'draft'::character varying,                           +
     gl_journal_id bigint,                                                                     +
     processed_by uuid,                                                                        +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE payroll_periods (                                                                +
     id bigint NOT NULL DEFAULT nextval('payroll_periods_id_seq'::regclass),                   +
     period_name varchar(50) NOT NULL,                                                         +
     payroll_type_id bigint NOT NULL,                                                          +
     start_date date NOT NULL,                                                                 +
     end_date date NOT NULL,                                                                   +
     status varchar(20) NOT NULL DEFAULT 'open'::character varying,                            +
     company_id bigint,                                                                        +
     is_closed boolean NOT NULL DEFAULT false,                                                 +
     created_by uuid,                                                                          +
     approved_by uuid,                                                                         +
     deleted_at timestamptz,                                                                   +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now()                                             +
 );                                                                                            +
 
 CREATE TABLE payroll_summary_gl_links (                                                       +
     id bigint NOT NULL DEFAULT nextval('payroll_summary_gl_links_id_seq'::regclass),          +
     payrun_id bigint NOT NULL,                                                                +
     employee_id bigint NOT NULL,                                                              +
     net_pay numeric(18,2) NOT NULL,                                                           +
     gl_journal_id bigint,                                                                     +
     posted_by uuid,                                                                           +
     posting_date_override date,                                                               +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE payroll_types (                                                                  +
     id bigint NOT NULL DEFAULT nextval('payroll_types_id_seq'::regclass),                     +
     name varchar(30) NOT NULL,                                                                +
     description text,                                                                         +
     deleted_at timestamptz,                                                                   +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now()                                             +
 );                                                                                            +
 
 CREATE TABLE penalty_rules (                                                                  +
     id bigint NOT NULL DEFAULT nextval('penalty_rules_id_seq'::regclass),                     +
     name varchar(100) NOT NULL,                                                               +
     description text,                                                                         +
     penalty_type varchar(50) NOT NULL,                                                        +
     penalty_value_numeric numeric(10,2),                                                      +
     penalty_value_text text,                                                                  +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE permissions (                                                                    +
     id bigint NOT NULL,                                                                       +
     name varchar(100) NOT NULL,                                                               +
     description text,                                                                         +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE pos_terminal_id (                                                                +
     id bigint NOT NULL DEFAULT nextval('pos_terminal_id_id_seq'::regclass),                   +
     terminal_code varchar(50) NOT NULL,                                                       +
     name varchar(120) NOT NULL,                                                               +
     branch_id bigint NOT NULL,                                                                +
     company_id bigint NOT NULL,                                                               +
     model_number varchar(100),                                                                +
     serial_number varchar(100),                                                               +
     ip_address varchar(45),                                                                   +
     status varchar(20) NOT NULL DEFAULT 'ACTIVE'::character varying,                          +
     notes text,                                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE price_list_items (                                                               +
     id bigint NOT NULL,                                                                       +
     price_list_id bigint NOT NULL,                                                            +
     item_id bigint NOT NULL,                                                                  +
     unit_of_measurement_id bigint,                                                            +
     unit_price numeric(18,4) NOT NULL,                                                        +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     valid_from date,                                                                          +
     valid_to date,                                                                            +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE price_lists (                                                                    +
     id bigint NOT NULL,                                                                       +
     name varchar(100) NOT NULL,                                                               +
     company_id bigint NOT NULL,                                                               +
     currency_id bigint NOT NULL,                                                              +
     is_default boolean NOT NULL DEFAULT false,                                                +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE profiles (                                                                       +
     profile_id uuid NOT NULL,                                                                 +
     username varchar(80) NOT NULL,                                                            +
     email varchar(120) NOT NULL,                                                              +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     last_login_at timestamptz,                                                                +
     favorite_screens ARRAY,                                                                   +
     user_mac_address varchar(17),                                                             +
     user_device_name text,                                                                    +
     device_hwid text,                                                                         +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz,                                                                   +
     employee_id bigint                                                                        +
 );                                                                                            +
 
 CREATE TABLE project_costs (                                                                  +
     id bigint NOT NULL,                                                                       +
     project_id bigint NOT NULL,                                                               +
     cost_date date NOT NULL,                                                                  +
     description varchar(150),                                                                 +
     amount numeric(18,2) NOT NULL,                                                            +
     gl_account_id bigint,                                                                     +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE project_tasks (                                                                  +
     id bigint NOT NULL,                                                                       +
     project_id bigint NOT NULL,                                                               +
     task_name varchar(100) NOT NULL,                                                          +
     description text,                                                                         +
     due_date date,                                                                            +
     status varchar(20) NOT NULL DEFAULT 'open'::character varying,                            +
     assigned_to bigint,                                                                       +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE projects (                                                                       +
     id bigint NOT NULL,                                                                       +
     project_name varchar(100) NOT NULL,                                                       +
     description text,                                                                         +
     start_date date,                                                                          +
     end_date date,                                                                            +
     status varchar(20) NOT NULL DEFAULT 'active'::character varying,                          +
     manager_id bigint,                                                                        +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE promotion_rules (                                                                +
     id bigint NOT NULL,                                                                       +
     promotion_id bigint NOT NULL,                                                             +
     rule_type varchar(50) NOT NULL,                                                           +
     item_id bigint,                                                                           +
     discount_rate numeric(5,2),                                                               +
     buy_quantity numeric(18,2),                                                               +
     get_quantity numeric(18,2),                                                               +
     rule_scope promotion_rule_scope_enum,                                                     +
     item_main_group_id bigint,                                                                +
     discount_type promotion_discount_type_enum,                                               +
     free_item_id bigint,                                                                      +
     min_cart_total numeric(18,2),                                                             +
     is_stackable boolean NOT NULL DEFAULT false,                                              +
     priority integer DEFAULT 0,                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     created_by uuid,                                                                          +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE promotions (                                                                     +
     id bigint NOT NULL,                                                                       +
     name varchar(100) NOT NULL,                                                               +
     description text,                                                                         +
     valid_from timestamptz,                                                                   +
     valid_to timestamptz,                                                                     +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     company_id bigint,                                                                        +
     branch_id bigint,                                                                         +
     customer_group_id bigint,                                                                 +
     promotion_type varchar(50) NOT NULL,                                                      +
     max_usage_limit integer,                                                                  +
     max_usage_per_customer integer,                                                           +
     stackable boolean NOT NULL DEFAULT false,                                                 +
     created_by uuid,                                                                          +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE purchase_funds (                                                                 +
     id bigint NOT NULL,                                                                       +
     company_id bigint NOT NULL,                                                               +
     branch_id bigint,                                                                         +
     supplier_id bigint,                                                                       +
     fund_number varchar(30) NOT NULL,                                                         +
     fund_date date NOT NULL,                                                                  +
     currency_id bigint,                                                                       +
     fund_amount numeric(18,2) NOT NULL,                                                       +
     bank_account_id bigint,                                                                   +
     purchase_order_id bigint,                                                                 +
     status varchar(20) NOT NULL DEFAULT 'OPEN'::character varying,                            +
     note text,                                                                                +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE purchase_order_details (                                                         +
     id bigint NOT NULL,                                                                       +
     purchase_order_id bigint NOT NULL,                                                        +
     item_id bigint NOT NULL,                                                                  +
     quantity numeric(18,2) NOT NULL,                                                          +
     unit_price numeric(18,4) NOT NULL,                                                        +
     total numeric(18,2) NOT NULL,                                                             +
     unit_id bigint,                                                                           +
     discount_rate numeric(5,2) DEFAULT 0,                                                     +
     tax_amount numeric(18,2) DEFAULT 0,                                                       +
     is_free_item boolean NOT NULL DEFAULT false,                                              +
     note text,                                                                                +
     licence_id bigint,                                                                        +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE purchase_order_header (                                                          +
     id bigint NOT NULL,                                                                       +
     branch_id bigint,                                                                         +
     supplier_id bigint,                                                                       +
     po_type_id bigint,                                                                        +
     po_number varchar(20) NOT NULL,                                                           +
     po_date date NOT NULL,                                                                    +
     currency_id bigint,                                                                       +
     company_id bigint NOT NULL,                                                               +
     approved_by_id uuid,                                                                      +
     approved_at timestamptz,                                                                  +
     expected_delivery_date date,                                                              +
     notes text,                                                                               +
     incoterm varchar(10),                                                                     +
     is_tax_inclusive boolean NOT NULL DEFAULT false,                                          +
     forecast_id bigint,                                                                       +
     total_amount numeric(18,2) NOT NULL,                                                      +
     status varchar(20) NOT NULL DEFAULT 'open'::character varying,                            +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz,                                                                   +
     shipping_method_id bigint                                                                 +
 );                                                                                            +
 
 CREATE TABLE purchase_order_tax_details (                                                     +
     id bigint NOT NULL,                                                                       +
     po_line_id bigint NOT NULL,                                                               +
     tax_code_id bigint NOT NULL,                                                              +
     tax_rate_id bigint,                                                                       +
     tax_amount numeric(18,2) NOT NULL,                                                        +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE purchase_order_types (                                                           +
     id bigint NOT NULL,                                                                       +
     name varchar(40) NOT NULL,                                                                +
     is_active boolean,                                                                        +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE qualification_types (                                                            +
     id bigint NOT NULL DEFAULT nextval('qualification_types_id_seq'::regclass),               +
     code varchar(10) NOT NULL,                                                                +
     name varchar(100) NOT NULL,                                                               +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE receipt_sms_log (                                                                +
     id bigint NOT NULL DEFAULT nextval('receipt_sms_log_id_seq'::regclass),                   +
     customer_receipt_id bigint NOT NULL,                                                      +
     customer_id bigint NOT NULL,                                                              +
     phone_number_used varchar(30) NOT NULL,                                                   +
     sms_content text NOT NULL,                                                                +
     whatsapp_delivered boolean NOT NULL DEFAULT false,                                        +
     telegram_delivered boolean NOT NULL DEFAULT false,                                        +
     sms_delivered boolean NOT NULL DEFAULT false,                                             +
     email_delivered boolean NOT NULL DEFAULT false,                                           +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now()                                             +
 );                                                                                            +
 
 CREATE TABLE retribution_types (                                                              +
     id bigint NOT NULL,                                                                       +
     name varchar(80) NOT NULL,                                                                +
     description text,                                                                         +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE return_types (                                                                   +
     id bigint NOT NULL DEFAULT nextval('return_types_id_seq'::regclass),                      +
     name varchar(100) NOT NULL,                                                               +
     description text,                                                                         +
     action_suggested varchar(50),                                                             +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE role_permissions (                                                               +
     role_id bigint NOT NULL,                                                                  +
     permission_id bigint NOT NULL,                                                            +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now()                                             +
 );                                                                                            +
 
 CREATE TABLE roles (                                                                          +
     id bigint NOT NULL,                                                                       +
     name varchar(80) NOT NULL,                                                                +
     description text,                                                                         +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE sales_area_representatives (                                                     +
     id bigint NOT NULL,                                                                       +
     employee_id bigint NOT NULL,                                                              +
     sales_area_id bigint,                                                                     +
     active boolean NOT NULL DEFAULT true,                                                     +
     created_at timestamptz DEFAULT now(),                                                     +
     updated_at timestamptz DEFAULT now(),                                                     +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE sales_areas (                                                                    +
     id bigint NOT NULL,                                                                       +
     branch_id bigint NOT NULL,                                                                +
     name varchar(80) NOT NULL,                                                                +
     created_at timestamptz DEFAULT now(),                                                     +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE sales_books (                                                                    +
     id bigint NOT NULL,                                                                       +
     branch_id bigint,                                                                         +
     book_type varchar(40) NOT NULL,                                                           +
     from_number integer NOT NULL,                                                             +
     to_number integer NOT NULL,                                                               +
     issued_at date NOT NULL,                                                                  +
     status text,                                                                              +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE sales_coupon_codes (                                                             +
     id bigint NOT NULL DEFAULT nextval('sales_coupon_codes_id_seq'::regclass),                +
     code varchar(50) NOT NULL,                                                                +
     description text,                                                                         +
     discount_type varchar(20) NOT NULL,                                                       +
     discount_value numeric(12,2) NOT NULL,                                                    +
     minimum_purchase_amount numeric(18,2) DEFAULT 0,                                          +
     valid_from timestamptz,                                                                   +
     valid_to timestamptz,                                                                     +
     max_uses_total integer,                                                                   +
     max_uses_per_customer integer,                                                            +
     uses_count integer NOT NULL DEFAULT 0,                                                    +
     company_id bigint,                                                                        +
     branch_id bigint,                                                                         +
     customer_id bigint,                                                                       +
     coupon_type varchar(50),                                                                  +
     created_by uuid,                                                                          +
     is_stackable boolean NOT NULL DEFAULT false,                                              +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     applicable_items jsonb,                                                                   +
     applicable_customers jsonb,                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE sales_order_details (                                                            +
     id bigint NOT NULL,                                                                       +
     order_id bigint NOT NULL,                                                                 +
     item_id bigint NOT NULL,                                                                  +
     description varchar(150),                                                                 +
     quantity numeric(18,2) NOT NULL,                                                          +
     currency_id bigint NOT NULL,                                                              +
     note text,                                                                                +
     unit_price numeric(18,4) NOT NULL,                                                        +
     discount numeric(18,2) DEFAULT 0,                                                         +
     unit_id bigint,                                                                           +
     combo_definition_id bigint,                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE sales_order_header (                                                             +
     id bigint NOT NULL,                                                                       +
     customer_id bigint NOT NULL,                                                              +
     company_id bigint NOT NULL,                                                               +
     branch_id bigint,                                                                         +
     quote_id bigint,                                                                          +
     order_number varchar(50) NOT NULL,                                                        +
     note text,                                                                                +
     order_date date NOT NULL,                                                                 +
     delivery_date date,                                                                       +
     status varchar(20) NOT NULL DEFAULT 'draft'::character varying,                           +
     sales_rep_id bigint,                                                                      +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz,                                                                   +
     shipping_method_id bigint,                                                                +
     sales_type_id bigint,                                                                     +
     sales_coupon_code_id bigint                                                               +
 );                                                                                            +
 
 CREATE TABLE sales_quote_details (                                                            +
     id bigint NOT NULL,                                                                       +
     quote_id bigint NOT NULL,                                                                 +
     item_id bigint NOT NULL,                                                                  +
     description varchar(150),                                                                 +
     quantity numeric(18,2) NOT NULL,                                                          +
     unit_price numeric(18,4) NOT NULL,                                                        +
     currency_id bigint NOT NULL,                                                              +
     note text,                                                                                +
     discount numeric(18,2) DEFAULT 0,                                                         +
     unit_id bigint,                                                                           +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE sales_quote_header (                                                             +
     id bigint NOT NULL,                                                                       +
     customer_id bigint NOT NULL,                                                              +
     quote_number varchar(50) NOT NULL,                                                        +
     company_id bigint NOT NULL,                                                               +
     quote_date date NOT NULL,                                                                 +
     expiry_date date,                                                                         +
     note text,                                                                                +
     status varchar(20) NOT NULL DEFAULT 'draft'::character varying,                           +
     sales_rep_id bigint,                                                                      +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE sales_returns_details (                                                          +
     id bigint NOT NULL DEFAULT nextval('sales_returns_details_id_seq'::regclass),             +
     sales_return_id bigint NOT NULL,                                                          +
     line_number smallint NOT NULL,                                                            +
     item_id bigint NOT NULL,                                                                  +
     unit_id bigint NOT NULL,                                                                  +
     returned_quantity numeric(18,2) NOT NULL,                                                 +
     unit_price_at_return numeric(18,4) NOT NULL,                                              +
     reason_for_return_item text,                                                              +
     condition_of_item varchar(50),                                                            +
     warehouse_id_returned_to bigint,                                                          +
     batch_number_returned varchar(40),                                                        +
     expiry_date_returned date,                                                                +
     is_approved_for_credit boolean NOT NULL DEFAULT false,                                    +
     notes text,                                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE sales_returns_header (                                                           +
     id bigint NOT NULL DEFAULT nextval('sales_returns_header_id_seq'::regclass),              +
     branch_id bigint NOT NULL,                                                                +
     customer_id bigint NOT NULL,                                                              +
     company_id bigint NOT NULL,                                                               +
     return_number varchar(30) NOT NULL,                                                       +
     return_date date NOT NULL,                                                                +
     original_invoice_id bigint,                                                               +
     original_sales_order_id bigint,                                                           +
     return_type_id bigint NOT NULL,                                                           +
     status varchar(30) NOT NULL DEFAULT 'PENDING_INSPECTION'::character varying,              +
     notes text,                                                                               +
     customer_credit_note_id bigint,                                                           +
     created_by_profile_id uuid,                                                               +
     updated_by_profile_id uuid,                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE sales_targets (                                                                  +
     id bigint NOT NULL,                                                                       +
     sales_rep_id bigint,                                                                      +
     target_year smallint NOT NULL,                                                            +
     target_month smallint NOT NULL,                                                           +
     target_amount numeric(18,2) NOT NULL DEFAULT 0,                                           +
     item_group_id bigint,                                                                     +
     created_at timestamptz DEFAULT now(),                                                     +
     updated_at timestamptz DEFAULT now(),                                                     +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE sales_types (                                                                    +
     id bigint NOT NULL DEFAULT nextval('sales_types_id_seq'::regclass),                       +
     name varchar(50) NOT NULL,                                                                +
     description text,                                                                         +
     requires_immediate_payment boolean NOT NULL DEFAULT false,                                +
     is_default boolean NOT NULL DEFAULT false,                                                +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE sample_release_details (                                                         +
     id bigint NOT NULL,                                                                       +
     release_id bigint NOT NULL,                                                               +
     item_id bigint NOT NULL,                                                                  +
     warehouse_id bigint NOT NULL,                                                             +
     batch_number varchar(40),                                                                 +
     quantity numeric(18,2) NOT NULL,                                                          +
     unit_id bigint,                                                                           +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE sample_release_header (                                                          +
     id bigint NOT NULL,                                                                       +
     request_id bigint,                                                                        +
     release_date date NOT NULL,                                                               +
     released_by uuid,                                                                         +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE sample_request_details (                                                         +
     id bigint NOT NULL,                                                                       +
     request_id bigint NOT NULL,                                                               +
     item_id bigint NOT NULL,                                                                  +
     quantity numeric(18,2) NOT NULL,                                                          +
     purpose varchar(150),                                                                     +
     unit_id bigint,                                                                           +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE sample_request_header (                                                          +
     id bigint NOT NULL,                                                                       +
     requested_by uuid NOT NULL,                                                               +
     request_date date NOT NULL,                                                               +
     approval_status varchar(20) NOT NULL DEFAULT 'pending'::character varying,                +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE serial_movements (                                                               +
     id bigint NOT NULL,                                                                       +
     serial_id bigint NOT NULL,                                                                +
     movement_type varchar(50) NOT NULL,                                                       +
     movement_date timestamptz NOT NULL,                                                       +
     from_location varchar(100),                                                               +
     to_location varchar(100),                                                                 +
     reference varchar(100),                                                                   +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE shipping_methods (                                                               +
     id bigint NOT NULL DEFAULT nextval('shipping_methods_id_seq'::regclass),                  +
     name varchar(100) NOT NULL,                                                               +
     description text,                                                                         +
     carrier_name varchar(100),                                                                +
     estimated_delivery_days integer,                                                          +
     tracking_url_template text,                                                               +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE stock_adjustment_details (                                                       +
     id bigint NOT NULL,                                                                       +
     adjustment_id bigint NOT NULL,                                                            +
     item_id bigint NOT NULL,                                                                  +
     bin_id bigint,                                                                            +
     batch_number varchar(40),                                                                 +
     system_quantity numeric(18,2) NOT NULL,                                                   +
     real_quantity numeric(18,2) NOT NULL,                                                     +
     diff_quantity numeric(18,2) NOT NULL,                                                     +
     reason text,                                                                              +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE stock_adjustment_header (                                                        +
     id bigint NOT NULL,                                                                       +
     warehouse_id bigint NOT NULL,                                                             +
     adjustment_date date NOT NULL,                                                            +
     notes varchar(150),                                                                       +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE stock_by_bin (                                                                   +
     id bigint NOT NULL,                                                                       +
     warehouse_id bigint NOT NULL,                                                             +
     bin_id bigint NOT NULL,                                                                   +
     item_id bigint NOT NULL,                                                                  +
     batch_number varchar(40),                                                                 +
     expiry_date date,                                                                         +
     quantity numeric(18,2) NOT NULL,                                                          +
     cost numeric(18,4),                                                                       +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE stock_transfer_details (                                                         +
     id bigint NOT NULL DEFAULT nextval('stock_transfer_details_id_seq'::regclass),            +
     stock_transfer_id bigint NOT NULL,                                                        +
     line_number smallint NOT NULL,                                                            +
     item_id bigint NOT NULL,                                                                  +
     unit_id bigint NOT NULL,                                                                  +
     quantity numeric(18,2) NOT NULL,                                                          +
     from_bin_id bigint,                                                                       +
     to_bin_id bigint,                                                                         +
     batch_number varchar(40),                                                                 +
     expiry_date date,                                                                         +
     notes text,                                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE stock_transfers (                                                                +
     id bigint NOT NULL,                                                                       +
     from_warehouse_id bigint NOT NULL,                                                        +
     to_warehouse_id bigint NOT NULL,                                                          +
     transfer_date date NOT NULL,                                                              +
     notes varchar(150),                                                                       +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE supervisor_salesperson (                                                         +
     supervisor_profile_id uuid NOT NULL,                                                      +
     salesperson_profile_id uuid NOT NULL                                                      +
 );                                                                                            +
 
 CREATE TABLE supplier_bill_details (                                                          +
     id bigint NOT NULL,                                                                       +
     bill_id bigint NOT NULL,                                                                  +
     item_id bigint NOT NULL,                                                                  +
     description varchar(150),                                                                 +
     quantity numeric(18,2) NOT NULL,                                                          +
     unit_price numeric(18,4) NOT NULL,                                                        +
     discount numeric(18,2) DEFAULT 0,                                                         +
     tax_amount numeric(18,2) DEFAULT 0,                                                       +
     total numeric(18,2) NOT NULL,                                                             +
     unit_id bigint,                                                                           +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE supplier_bill_header (                                                           +
     id bigint NOT NULL,                                                                       +
     supplier_id bigint NOT NULL,                                                              +
     company_id bigint NOT NULL,                                                               +
     bill_number varchar(50) NOT NULL,                                                         +
     bill_date date NOT NULL,                                                                  +
     incoterm varchar(10),                                                                     +
     due_date date,                                                                            +
     total_amount numeric(18,2) NOT NULL,                                                      +
     status varchar(20) NOT NULL DEFAULT 'open'::character varying,                            +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE supplier_categories (                                                            +
     id bigint NOT NULL,                                                                       +
     name varchar(100) NOT NULL,                                                               +
     description text,                                                                         +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE supplier_debit_notes (                                                           +
     id bigint NOT NULL,                                                                       +
     supplier_id bigint NOT NULL,                                                              +
     bill_id bigint,                                                                           +
     debit_note_number varchar(50) NOT NULL,                                                   +
     debit_note_date date NOT NULL,                                                            +
     amount numeric(18,2) NOT NULL,                                                            +
     reason varchar(150),                                                                      +
     status varchar(20) NOT NULL DEFAULT 'open'::character varying,                            +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE supplier_payments (                                                              +
     id bigint NOT NULL,                                                                       +
     supplier_id bigint NOT NULL,                                                              +
     payment_number varchar(50) NOT NULL,                                                      +
     payment_date date NOT NULL,                                                               +
     amount numeric(18,2) NOT NULL,                                                            +
     payment_mode varchar(20) NOT NULL,                                                        +
     reference varchar(60),                                                                    +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE suppliers (                                                                      +
     id bigint NOT NULL,                                                                       +
     code varchar(30) NOT NULL,                                                                +
     name varchar(150) NOT NULL,                                                               +
     address_line1 varchar(255),                                                               +
     address_line2 varchar(255),                                                               +
     country_id bigint,                                                                        +
     postal_code varchar(20),                                                                  +
     phone varchar(30),                                                                        +
     email varchar(120),                                                                       +
     currency_id bigint,                                                                       +
     supplier_category_id bigint NOT NULL,                                                     +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE sync_procedure_log (                                                             +
     id bigint NOT NULL DEFAULT nextval('sync_procedure_log_id_seq'::regclass),                +
     branch_id bigint NOT NULL,                                                                +
     sync_time timestamptz NOT NULL,                                                           +
     synced_by uuid                                                                            +
 );                                                                                            +
 
 CREATE TABLE system_configuration (                                                           +
     config_key varchar(100) NOT NULL,                                                         +
     config_value text,                                                                        +
     description text,                                                                         +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE tax_codes (                                                                      +
     id bigint NOT NULL,                                                                       +
     code varchar(20) NOT NULL,                                                                +
     description varchar(120),                                                                 +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE tax_rates (                                                                      +
     id bigint NOT NULL,                                                                       +
     tax_code_id bigint NOT NULL,                                                              +
     rate numeric(7,4) NOT NULL,                                                               +
     effective_date date NOT NULL,                                                             +
     expiry_date date,                                                                         +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE timesheets (                                                                     +
     id bigint NOT NULL,                                                                       +
     employee_id bigint NOT NULL,                                                              +
     project_id bigint,                                                                        +
     task_id bigint,                                                                           +
     work_date date NOT NULL,                                                                  +
     hours_worked numeric(5,2) NOT NULL,                                                       +
     notes text,                                                                               +
     status varchar(20) NOT NULL DEFAULT 'draft'::character varying,                           +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE units_of_measure (                                                               +
     id bigint NOT NULL,                                                                       +
     code varchar(20) NOT NULL,                                                                +
     name varchar(80) NOT NULL,                                                                +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE user_notification_tokens (                                                       +
     id bigint NOT NULL DEFAULT nextval('user_notification_tokens_id_seq'::regclass),          +
     user_id uuid NOT NULL,                                                                    +
     token text NOT NULL,                                                                      +
     provider varchar(50) NOT NULL,                                                            +
     device_info text,                                                                         +
     last_used_at timestamptz,                                                                 +
     deleted_at timestamptz,                                                                   +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now()                                             +
 );                                                                                            +
 
 CREATE TABLE user_permissions (                                                               +
     user_id uuid NOT NULL,                                                                    +
     permission_id bigint NOT NULL,                                                            +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now()                                             +
 );                                                                                            +
 
 CREATE TABLE user_roles (                                                                     +
     user_id uuid NOT NULL,                                                                    +
     role_id bigint NOT NULL,                                                                  +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now()                                             +
 );                                                                                            +
 
 CREATE TABLE voucher_state_types (                                                            +
     id smallint NOT NULL DEFAULT nextval('voucher_state_types_id_seq'::regclass),             +
     name varchar(40) NOT NULL,                                                                +
     description text,                                                                         +
     sequence_order smallint,                                                                  +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE voucher_states (                                                                 +
     id bigint NOT NULL,                                                                       +
     voucher_id bigint NOT NULL,                                                               +
     state_id smallint NOT NULL,                                                               +
     changed_by uuid,                                                                          +
     changed_at timestamptz NOT NULL DEFAULT now(),                                            +
     note varchar(150),                                                                        +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE voucher_types (                                                                  +
     id bigint NOT NULL DEFAULT nextval('voucher_types_id_seq'::regclass),                     +
     code varchar(10) NOT NULL,                                                                +
     name varchar(100) NOT NULL,                                                               +
     description text,                                                                         +
     is_system_type boolean NOT NULL DEFAULT false,                                            +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE waiting_list (                                                                   +
     id bigint NOT NULL,                                                                       +
     voucher_id bigint NOT NULL,                                                               +
     state_id smallint,                                                                        +
     assigned_to uuid,                                                                         +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz,                                                                   +
     comments varchar(150)                                                                     +
 );                                                                                            +
 
 CREATE TABLE warehouse_bins (                                                                 +
     id bigint NOT NULL,                                                                       +
     warehouse_id bigint NOT NULL,                                                             +
     bin_type_id bigint,                                                                       +
     bin_code varchar(50) NOT NULL,                                                            +
     description varchar(150),                                                                 +
     is_pick_face boolean NOT NULL DEFAULT false,                                              +
     is_active boolean NOT NULL DEFAULT true,                                                  +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE warehouses (                                                                     +
     id bigint NOT NULL,                                                                       +
     company_id bigint NOT NULL,                                                               +
     branch_id bigint NOT NULL,                                                                +
     city_id bigint,                                                                           +
     name text NOT NULL,                                                                       +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE workflow_actions (                                                               +
     id bigint NOT NULL,                                                                       +
     instance_id bigint NOT NULL,                                                              +
     step_id bigint NOT NULL,                                                                  +
     action_type varchar(20) NOT NULL,                                                         +
     action_by uuid NOT NULL,                                                                  +
     action_at timestamptz NOT NULL DEFAULT now(),                                             +
     attachment_url text,                                                                      +
     notes text,                                                                               +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE workflow_definitions (                                                           +
     id bigint NOT NULL,                                                                       +
     name varchar(100) NOT NULL,                                                               +
     entity_type varchar(50) NOT NULL,                                                         +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     description text,                                                                         +
     company_id bigint NOT NULL,                                                               +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE workflow_instances (                                                             +
     id bigint NOT NULL,                                                                       +
     workflow_id bigint NOT NULL,                                                              +
     entity_type varchar(50) NOT NULL,                                                         +
     entity_id bigint NOT NULL,                                                                +
     current_step_id bigint,                                                                   +
     status varchar(20) NOT NULL DEFAULT 'pending'::character varying,                         +
     started_at timestamptz NOT NULL DEFAULT now(),                                            +
     initiated_by uuid,                                                                        +
     last_action_by uuid,                                                                      +
     company_id bigint NOT NULL,                                                               +
     completed_at timestamptz,                                                                 +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
 CREATE TABLE workflow_steps (                                                                 +
     id bigint NOT NULL,                                                                       +
     workflow_id bigint NOT NULL,                                                              +
     step_number smallint NOT NULL,                                                            +
     step_name varchar(100) NOT NULL,                                                          +
     is_final boolean,                                                                         +
     is_optional boolean,                                                                      +
     approver_role varchar(50),                                                                +
     approver_user_id uuid,                                                                    +
     created_at timestamptz NOT NULL DEFAULT now(),                                            +
     updated_at timestamptz NOT NULL DEFAULT now(),                                            +
     deleted_at timestamptz                                                                    +
 );                                                                                            +
 
(192 rows)

