import { GitHubBanner, Refine, Authenticated, ErrorComponent } from "@refinedev/core";
import { DevtoolsPanel, DevtoolsProvider } from "@refinedev/devtools";
import { RefineKbar, RefineKbarProvider } from "@refinedev/kbar";

import routerBindings, {
  DocumentTitleHandler,
  UnsavedChangesNotifier,
  NavigateToResource,
  CatchAllNavigate,
} from "@refinedev/react-router";
import { dataProvider, liveProvider } from "@refinedev/supabase";
import { BrowserRouter, Route, Routes, Outlet } from "react-router";
import "./App.css";
import authProvider from "./authProvider";
import { supabaseClient } from "./utility";
import { ProfilesList, ProfilesCreate, ProfilesEdit, ProfilesShow } from "./pages/profiles";
import { CompaniesList, CompaniesCreate, CompaniesEdit, CompaniesShow } from "./pages/companies";
import { LoginPage } from "./components/auth/LoginPage";

function App() {
  return (
    <BrowserRouter>
      <RefineKbarProvider>
        <DevtoolsProvider>
          <Refine
            dataProvider={dataProvider(supabaseClient)}
            liveProvider={liveProvider(supabaseClient)}
            authProvider={authProvider}
            routerProvider={routerBindings}
            resources={[
              {
                name: "profiles",
                list: "/profiles",
                create: "/profiles/create",
                edit: "/profiles/edit/:id",
                show: "/profiles/show/:id",
                meta: {
                  canDelete: true,
                },
              },
              {
                name: "companies",
                list: "/companies",
                create: "/companies/create",
                edit: "/companies/edit/:id",
                show: "/companies/show/:id",
                meta: {
                  canDelete: true,
                },
              },
            ]}
            options={{
              syncWithLocation: true,
              warnWhenUnsavedChanges: true,
              useNewQueryKeys: true,
              projectId: "tWK0fd-Mv5s4N-fBgZsm",
            }}
          >
            <Routes>
              <Route
                element={
                  <Authenticated key="authenticated-inner" fallback={<CatchAllNavigate to="/login" />}>
                    <Outlet />
                  </Authenticated>
                }
              >
                <Route index element={<NavigateToResource resource="profiles" />} />

                {/* Profiles Routes */}
                <Route path="/profiles">
                  <Route index element={<ProfilesList />} />
                  <Route path="create" element={<ProfilesCreate />} />
                  <Route path="edit/:id" element={<ProfilesEdit />} />
                  <Route path="show/:id" element={<ProfilesShow />} />
                </Route>

                {/* Companies Routes */}
                <Route path="/companies">
                  <Route index element={<CompaniesList />} />
                  <Route path="create" element={<CompaniesCreate />} />
                  <Route path="edit/:id" element={<CompaniesEdit />} />
                  <Route path="show/:id" element={<CompaniesShow />} />
                </Route>

                <Route path="*" element={<ErrorComponent />} />
              </Route>
              <Route
                element={
                  <Authenticated key="authenticated-outer" fallback={<Outlet />}>
                    <NavigateToResource />
                  </Authenticated>
                }
              >
                <Route path="/login" element={<LoginPage />} />
                <Route path="/register" element={<div>Register Page</div>} />
                <Route path="/forgot-password" element={<div>Forgot Password</div>} />
              </Route>
            </Routes>
            <RefineKbar />
            <UnsavedChangesNotifier />
            <DocumentTitleHandler />
          </Refine>
          <DevtoolsPanel />
        </DevtoolsProvider>
      </RefineKbarProvider>
    </BrowserRouter>
  );
}

export default App;
